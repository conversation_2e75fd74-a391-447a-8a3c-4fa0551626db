<template>
  <div class="new-class" ref="classView">
    <div class="page-header-container">
      <PageHeader :title="isEditing ? 'Editar turma' : 'Nova turma'">
        <template #actions>
          <BackButton @click="goBack" />
        </template>
      </PageHeader>
    </div>

    <!-- Alerta de validação -->
    <div v-if="validationAlert.show" class="validation-alert">
      <i class="fas fa-exclamation-triangle"></i>
      <span>{{ validationAlert.message }}</span>
    </div>

    <!-- Configurações Gerais -->
    <div class="section-container">
      <h2 class="section-title">CONFIGURAÇÕES GERAIS</h2>

      <!-- Primeira linha: Nome da turma -->
      <div class="form-group mb-3">
        <div class="label-with-help">
          <label class="form-label">Nome da turma</label>
          <i class="icon fa fa-exclamation-circle text-danger fa-fw " title="Obrigatório" role="img"
            aria-label="Obrigatório"></i>
          <HelpIcon title="Ajuda com nome da turma" text="Insira um nome para a turma. Exemplo: Turma ADM 2025." />
        </div>
        <div class="limited-width-input" style="max-width: 280px;">
          <CustomInput v-model="classData.classname" placeholder="Digite o nome da turma" :width="280" required
            ref="classnameInput" :has-error="formErrors.classname.hasError"
            :error-message="formErrors.classname.message" @validate="validateField('classname')" />
        </div>
      </div>

      <!-- Segunda linha: Data início, Data fim, Habilitar data fim -->
      <div class="form-row mb-3">
        <div class="form-group">
          <div class="label-with-help">
            <label class="form-label">Data início da turma</label>
            <i class="icon fa fa-exclamation-circle text-danger fa-fw " title="Obrigatório" role="img"
              aria-label="Obrigatório"></i>
            <HelpIcon title="Ajuda com data início da turma"
              text="Insira uma data de início para a turma. Exemplo: 16/03/2025." />
          </div>
          <div class="limited-width-input">
            <CustomInput v-model="classData.startdate" type="date" :width="180" required class="date-input"
              ref="startdateInput" :has-error="formErrors.startdate.hasError"
              :error-message="formErrors.startdate.message" @validate="validateField('startdate')" />
          </div>
        </div>

        <div class="form-group" :class="{ 'disabled': !classData.optional_fields.enableenddate }">
          <div class="label-with-help">
            <label class="form-label">Data fim da turma</label>
            <i class="icon fa fa-exclamation-circle text-danger fa-fw " title="Obrigatório" role="img"
              aria-label="Obrigatório"></i>
            <HelpIcon title="Ajuda com data fim da turma"
              text="Insira uma data fim para a turma. Exemplo: 16/12/2025." />
          </div>
          <div class="input-with-checkbox">
            <div class="limited-width-input">
              <CustomInput v-model="classData.optional_fields.enddate" type="date" :width="180"
                :disabled="!classData.optional_fields.enableenddate" required class="date-input"
                :has-error="formErrors.enddate.hasError" :error-message="formErrors.enddate.message"
                @validate="validateField('enddate')" />
            </div>
            <CustomCheckbox v-model="classData.optional_fields.enableenddate" id="enableEndDate"
              label="Habilitar data fim da turma" class="inline-checkbox" :disabled="false" />
          </div>
        </div>
      </div>

      <!-- Terceira linha: Pré-inscrição -->
      <div class="form-row mb-3">
        <div class="form-group" :class="{ 'disabled': !classData.optional_fields.enablepreenrolment }"
          v-if="classData.enrol == 'offer_self'">
          <div class="label-with-help">
            <label class="form-label">Data início pré-inscrição</label>
            <HelpIcon title="Ajuda com data início pré-inscrição" text="Data de início do período de pré-inscrição." />
          </div>
          <CustomInput v-model="classData.optional_fields.preenrolmentstartdate" type="date" :width="180"
            :disabled="!classData.optional_fields.enablepreenrolment" class="date-input"
            :has-error="formErrors.preenrolmentstartdate.hasError"
            :error-message="formErrors.preenrolmentstartdate.message"
            @validate="validateField('preenrolmentstartdate')" />
        </div>

        <div class="form-group" :class="{ 'disabled': !classData.optional_fields.enablepreenrolment }"
          v-if="classData.enrol == 'offer_self'">
          <div class="label-with-help">
            <label class="form-label">Data fim pré-inscrição</label>
            <HelpIcon title="Ajuda com data fim pré-inscrição" text="Data de término do período de pré-inscrição." />
          </div>
          <CustomInput v-model="classData.optional_fields.preenrolmentenddate" type="date" :width="180"
            :disabled="!classData.optional_fields.enablepreenrolment" class="date-input"
            :has-error="formErrors.preenrolmentenddate.hasError" :error-message="formErrors.preenrolmentenddate.message"
            @validate="validateField('preenrolmentenddate')" />
        </div>

        <div class="form-group" v-if="classData.enrol == 'offer_self'">
          <div class="label-with-help">
            <label class="form-label">&nbsp;</label>
          </div>
          <CustomCheckbox v-model="classData.optional_fields.enablepreenrolment" id="enablePreEnrolment"
            label="Habilitar pré-inscrição" :disabled="false" />
        </div>
      </div>

      <!-- Quarta linha: Descrição -->
      <div class="form-group mb-3">
        <div class="label-with-help">
          <label class="form-label">Descrição da turma</label>
          <HelpIcon title="Ajuda com descrição da turma"
            text="Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025." />
        </div>
        <div class="limited-width-editor">
          <TextEditor v-model="classData.optional_fields.description" placeholder="Digite a descrição da turma aqui..."
            :rows="5" :disabled="false" />
        </div>
      </div>

      <!-- Quinta linha: Mínimo/Máximo de usuários e Papel padrão -->
      <div class="form-row mb-3">
        <div class="form-group" v-if="classData.enrol == 'offer_self'">
          <div class="label-with-help">
            <label class="form-label">Mínimo de usuários inscritos</label>
            <HelpIcon title="Ajuda com mínimo de usuários" text="Número mínimo de usuários para a turma." />
          </div>
          <div class="limited-width-input">
            <CustomInput v-model="classData.optional_fields.minusers" type="number" :width="180" />
          </div>
        </div>

        <div class="form-group" v-if="classData.enrol == 'offer_self'">
          <div class="label-with-help">
            <label class="form-label">Máximo de usuários inscritos</label>
            <HelpIcon title="Ajuda com máximo de usuários" text="Número máximo de usuários para a turma." />
          </div>
          <div class="limited-width-input">
            <CustomInput v-model="classData.optional_fields.maxusers" type="number" :width="180" />
          </div>
        </div>

        <div class="form-group">
          <div class="label-with-help">
            <label class="form-label">Papel atribuído por padrão</label>
            <i class="icon fa fa-exclamation-circle text-danger fa-fw " title="Obrigatório" role="img"
              aria-label="Obrigatório"></i>
            <HelpIcon title="Ajuda com papel atribuído por padrão"
              text="O papel atribuído será o papel que o usuário receberá dentro do curso após sua matrícula na turma. Exemplo: Estudante, Professor, Coordenador, etc…" />
          </div>
          <div class="limited-width-input">
            <CustomSelect v-model="classData.optional_fields.roleid" :options="roleOptions" :width="180" required
              :has-error="formErrors.roleid.hasError" :error-message="formErrors.roleid.message"
              @validate="validateField('roleid')" />
          </div>
        </div>
      </div>

      <!-- Sexta linha: Prazo de conclusão -->
      <div class="form-row mb-3">
        <div class="form-group" :class="{ 'disabled': !classData.optional_fields.enableenrolperiod }">
          <div class="label-with-help">
            <label class="form-label">Prazo de conclusão da turma</label>
            <i class="icon fa fa-exclamation-circle text-danger fa-fw " title="Obrigatório" role="img"
              aria-label="Obrigatório"></i>
            <HelpIcon title="Ajuda com prazo de conclusão da turma"
              :text="'O prazo de conclusão refere-se a quantidade de dias que um usuário terá para realizar o curso. Após esse período, se o usuário não obtiver uma situação de êxito na matrícula (Aprovado ou Concluído) durante seu progresso no curso, o sistema encerrará sua matrícula.' + (maxEnrolPeriod ? ` O valor máximo permitido é de ${maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.` : '')" />
          </div>
          <div class="input-with-checkbox">
            <div class="limited-width-input">
              <CustomInput v-model="classData.optional_fields.enrolperiod" type="number" :width="180"
                :disabled="!classData.optional_fields.enableenrolperiod" required
                :has-error="formErrors.enrolperiod.hasError" :error-message="formErrors.enrolperiod.message"
                :max="maxEnrolPeriod" @validate="validateField('enrolperiod')" />
            </div>
            <CustomCheckbox v-model="classData.optional_fields.enableenrolperiod" id="enableEnrolPeriod"
              label="Habilitar prazo de conclusão" class="inline-checkbox" :disabled="shouldDisableEnrolPeriod"
              v-tooltip="shouldDisableEnrolPeriod ? 'Não é possível habilitar prazo de conclusão para turmas de um dia (data início = data fim)' : ''" />
          </div>
        </div>
      </div>
    </div>

    <!-- Prorrogação -->
    <div class="section-container">
      <!-- Sétima linha: Habilitar prorrogação -->
      <div class="form-row mb-3">
        <div class="form-group"
          :class="{ 'disabled': !classData.optional_fields.enableenrolperiod, 'dependent-field': true }">
          <div class="label-with-help">
            <label class="form-label">Prorrogação de matrícula</label>
            <HelpIcon title="Ajuda com prorrogação de matrícula"
              text="A prorrogação estende o Prazo de conclusão do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou. Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado." />
          </div>
          <CustomCheckbox v-model="classData.optional_fields.enableextension" id="enableExtension"
            label="Habilitar Prorrogação de matrícula" :disabled="!classData.optional_fields.enableenrolperiod"
            v-tooltip="!classData.optional_fields.enableenrolperiod ? 'É necessário habilitar o Prazo de conclusão da turma primeiro' : ''" />
        </div>
      </div>

      <!-- Oitava linha: Dias de prorrogação -->
      <div class="form-row mb-3">
        <div class="form-group"
          :class="{ 'disabled': !classData.optional_fields.enableextension || !classData.optional_fields.enableenrolperiod, 'dependent-field': true }">
          <div class="label-with-help">
            <label class="form-label">Quantos dias serão acrescentados para prorrogação?</label>
            <i class="icon fa fa-exclamation-circle text-danger fa-fw " title="Obrigatório" role="img"
              aria-label="Obrigatório"></i>
          </div>
          <div class="limited-width-input">
            <CustomInput v-model="classData.optional_fields.extensionperiod" type="number" :width="180"
              :disabled="!classData.optional_fields.enableextension || !classData.optional_fields.enableenrolperiod"
              required :has-error="formErrors.extensionperiod.hasError"
              :error-message="formErrors.extensionperiod.message" @validate="validateField('extensionperiod')" />
          </div>
        </div>
      </div>

      <!-- Décima linha: Dias antes do término -->
      <div class="form-row mb-3">
        <div class="form-group"
          :class="{ 'disabled': !classData.optional_fields.enableextension || !classData.optional_fields.enableenrolperiod, 'dependent-field': true }">
          <div class="label-with-help">
            <label class="form-label">Quantos dias antes do término do prazo de matrícula o botão de prorrogação deve
              ser
              exibido?</label>
            <i class="icon fa fa-exclamation-circle text-danger fa-fw " title="Obrigatório" role="img"
              aria-label="Obrigatório"></i>
          </div>
          <div class="limited-width-input">
            <CustomInput v-model="classData.optional_fields.extensiondaysavailable" type="number" :width="180"
              :disabled="!classData.optional_fields.enableextension || !classData.optional_fields.enableenrolperiod"
              required :has-error="formErrors.extensiondaysavailable.hasError"
              :error-message="formErrors.extensiondaysavailable.message"
              @validate="validateField('extensiondaysavailable')" />
          </div>
        </div>
      </div>

      <!-- Décima primeira linha: Máximo de solicitações -->
      <div class="form-row mb-3">
        <div class="form-group"
          :class="{ 'disabled': !classData.optional_fields.enableextension || !classData.optional_fields.enableenrolperiod, 'dependent-field': true }">
          <div class="label-with-help">
            <label class="form-label">Quantas vezes o usuário pode pedir prorrogação?</label>
            <i class="icon fa fa-exclamation-circle text-danger fa-fw " title="Obrigatório" role="img"
              aria-label="Obrigatório"></i>
          </div>
          <div class="limited-width-input">
            <CustomInput v-model="classData.optional_fields.extensionmaxrequests" type="number" :width="180"
              :disabled="!classData.optional_fields.enableextension || !classData.optional_fields.enableenrolperiod"
              required :has-error="formErrors.extensionmaxrequests.hasError"
              :error-message="formErrors.extensionmaxrequests.message"
              @validate="validateField('extensionmaxrequests')" />
          </div>
        </div>
      </div>

      <!-- Nona linha: Situações permitidas -->
      <div class="form-group mb-3"
        :class="{ 'disabled': !classData.optional_fields.enableextension || !classData.optional_fields.enableenrolperiod, 'dependent-field': true }">
        <div class="label-with-help">
          <label class="form-label">Para quais situações de matrícula é permitida a prorrogação?</label>
        </div>
        <div class="limited-width-select">
          <Autocomplete v-model="extensionSituations" :items="extensionSituationOptions"
            placeholder="Selecione as situações..."
            :disabled="!classData.optional_fields.enableextension || !classData.optional_fields.enableenrolperiod"
            :width="280" :show-all-option="true" :auto-open="false" @select-all="handleSelectAllExtensionSituations" />
        </div>
      </div>
    </div>

    <!-- Rematrícula -->
    <div class="section-container" v-if="classData.enrol == 'offer_self'">
      <!-- Décima segunda linha: Habilitar rematrícula -->
      <div class="form-row mb-3">
        <div class="form-group">
          <div class="label-with-help">
            <label class="form-label">Habilitar rematrícula</label>
            <HelpIcon title="Ajuda com habilitar rematrícula"
              text="Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela." />
          </div>
          <CustomCheckbox v-model="classData.optional_fields.enablereenrol" id="enableReenrol"
            label="Habilitar rematrícula" :disabled="false" />
        </div>
      </div>

      <!-- Décima terceira linha: Situações permitidas -->
      <div class="form-group mb-3" :class="{ 'disabled': !classData.optional_fields.enablereenrol }">
        <div class="label-with-help">
          <label class="form-label">Quais situações de matrícula permitem rematrícula?</label>
        </div>
        <div class="limited-width-select">
          <Autocomplete v-model="reenrolSituations" :items="situationOptions" placeholder="Selecione as situações..."
            :disabled="!classData.optional_fields.enablereenrol" :width="280" :show-all-option="true" :auto-open="false"
            @select-all="handleSelectAllReenrolSituations" />
        </div>
      </div>
    </div>

    <!-- Corpo Docente -->
    <div class="section-container">
      <!-- Décima quarta linha: Atribuir corpo docente -->
      <div class="form-group mb-3">
        <div class="label-with-help">
          <label class="form-label">Atribuir corpo docente</label>
          <HelpIcon title="Ajuda com atribuir corpo docente"
            text="Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”." />
        </div>
        <div class="limited-width-select">
          <div class="custom-input-container" ref="teacherSearchContainer">
            <div class="input-wrapper with-icon">
              <input type="text" v-model="teacherSearchTerm" placeholder="Pesquisar ..."
                class="form-control custom-input" @input="handleTeacherInput" @focus="handleTeacherInputFocus"
                @keydown="handleKeydown" ref="teacherSearchInput">
            </div>
            <!-- Dropdown de professores -->
            <div v-if="showTeacherDropdown && teacherList.length > 0"
                 class="teacher-dropdown"
                 ref="teacherDropdown">
              <div v-for="(teacher, index) in teacherList"
                   :key="teacher.id"
                   class="teacher-dropdown-item"
                   :class="{ 'highlighted': highlightedIndex === index }"
                   @click="selectTeacher(teacher)"
                   @mouseenter="highlightedIndex = index">
                <div class="">
                  <div class="">{{ teacher.fullname }}</div>
                  <div class="" v-if="teacher.email">{{ teacher.email }}</div>
                </div>
              </div>
            </div>
            <!-- Mensagem quando não há resultados -->
            <div v-if="showTeacherDropdown && teacherSearchTerm.length >= 3"
                 class="teacher-dropdown">
              <div class="">Nenhum professor encontrado</div>
            </div>
          </div>
          <div class="my-2">
            <a v-for="teacher in selectedTeachers" :key="teacher.id"
              class="tag badge bg-primary text-white p-2 cursor-pointer" @click="removeTeacher(teacher.id)">
              <i class="fas fa-times mr-1"></i>
              {{ teacher.fullname }}
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Notificações Programadas - Removido temporariamente -->
    <!-- Esta seção foi removida porque o backend não suporta o campo enablenotifications -->
    <!-- Será implementado quando o backend for atualizado para suportar este campo -->

    <!-- Mensagem de campos obrigatórios -->
    <div class="required-fields-message">
      <div class="form-info">
        Este formulário contém campos obrigatórios marcados com <i class="fa fa-exclamation-circle text-danger"></i>
      </div>
    </div>

    <!-- Ações -->
    <div class="actions-container">
      <CustomButton variant="primary" label="Salvar" :loading="loading" @click="saveClass" />
      <CustomButton variant="secondary" label="Cancelar" @click="goBack" />
      <!-- Botão de matricular usuários removido -->
    </div>

    <!-- Loading -->
    <div class="loading" v-if="loading">
      <div class="spinner-border" role="status">
        <span class="sr-only">Carregando...</span>
      </div>
    </div>

    <!-- Toast -->
    <Toast :show="showToast" :message="toastMessage" :type="toastType" :duration="3000" />

    <!-- Modal de Matrículas removido -->
  </div>
</template>

<script>
import {
  addClass,
  updateClass,
  getClass,
  getClasses,
  getCourse,
  getSituationList,
  getCourseRoles,
  getClassMethods
} from '@/services/offer'
import { getPotentialTeachers } from '@/services/offer'
import CustomInput from '@/components/CustomInput.vue'
import CustomSelect from '@/components/CustomSelect.vue'
import CustomButton from '@/components/CustomButton.vue'
import PageHeader from '@/components/PageHeader.vue'
import BackButton from '@/components/BackButton.vue'
import Autocomplete from '@/components/Autocomplete.vue'
import TextEditor from '@/components/TextEditor.vue'
import CustomCheckbox from '@/components/CustomCheckbox.vue'
import FilterRow from '@/components/FilterRow.vue'
import FilterGroup from '@/components/FilterGroup.vue'
import Toast from '@/components/Toast.vue'
import HelpIcon from '@/components/HelpIcon.vue'
import FilterTag from '@/components/FilterTag.vue'
import FilterTags from '@/components/FilterTags.vue'
// EnrolmentModal removido

import { useRouter, useRoute } from 'vue-router'

export default {
  name: 'NewClassView',

  components: {
    CustomInput,
    CustomSelect,
    CustomButton,
    PageHeader,
    BackButton,
    Autocomplete,
    TextEditor,
    CustomCheckbox,
    FilterRow,
    FilterGroup,
    Toast,
    HelpIcon,
    FilterTag,
    FilterTags,
    // EnrolmentModal removido
  },

  setup() {
    const router = useRouter()
    const route = useRoute()
    return { router, route }
  },

  directives: {
    tooltip: {
      mounted(el, binding) {
        el.setAttribute('title', binding.value);
      },
      updated(el, binding) {
        el.setAttribute('title', binding.value);
      }
    }
  },

  props: {
    offercourseid: {
      type: [Number, String],
      required: true
    },
    classid: {
      type: [Number, String],
      required: false,
      default: null
    },
    offerid: {
      type: [Number, String],
      required: false,
      default: null
    }
  },

  data() {
    return {
      loading: false,
      showToast: false,
      toastMessage: '',
      toastType: 'success',
      toastTimeout: null,

      // Controle de modo de edição
      isEditing: false,
      classId: null,

      // Modal de matrículas removido

      // Métodos de inscrição disponíveis
      enrolmentMethods: [],

      // Dados da turma
      classData: {
        enrol: '', // Será definido pelo usuário
        offercourseid: null,
        classname: '',
        startdate: '',
        teachers: [],
        optional_fields: {
          enableenddate: false,
          enddate: '',
          enablepreenrolment: false,
          preenrolmentstartdate: '',
          preenrolmentenddate: '',
          description: '',
          enableenrolperiod: false,
          enrolperiod: null,
          minusers: null,
          maxusers: null,
          roleid: null,
          enablereenrol: false,
          reenrolmentsituations: [],
          enableextension: false,
          extensionperiod: null,
          extensiondaysavailable: null,
          extensionmaxrequests: null,
          extensionallowedsituations: []
        }
      },

      // Professores selecionados (para o Autocomplete)
      selectedTeachers: [],
      teacherSearchTerm: '',
      debounceTimer: null,
      teacherList: [],

      // Controle do dropdown de professores
      showTeacherDropdown: false,
      highlightedIndex: -1,

      // Situações selecionadas para prorrogação e rematrícula
      extensionSituations: [],
      reenrolSituations: [],

      // Opções de papéis
      roleOptions: [],

      // Opções de situações
      situationOptions: [],

      // Opções de situações específicas para prorrogação
      extensionSituationOptions: [],

      // Informações do curso da oferta
      offerCourse: null,

      // Controle de validação de campos
      formErrors: {
        enrol: { hasError: false, message: 'Método de inscrição é obrigatório' },
        classname: { hasError: false, message: 'Nome da turma é obrigatório' },
        startdate: { hasError: false, message: 'Data de início é obrigatória' },
        roleid: { hasError: false, message: 'Papel padrão é obrigatório' },
        enddate: { hasError: false, message: 'Data fim da turma é obrigatória quando habilitada' },
        preenrolmentstartdate: { hasError: false, message: 'Data início de pré-inscrição é obrigatória quando habilitada' },
        preenrolmentenddate: { hasError: false, message: 'Data fim de pré-inscrição é obrigatória quando habilitada' },
        enrolperiod: { hasError: false, message: 'Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma' },
        extensionperiod: { hasError: false, message: 'Dias para prorrogação é obrigatório quando habilitada' },
        extensiondaysavailable: { hasError: false, message: 'Dias antes do término é obrigatório quando habilitada' },
        extensionmaxrequests: { hasError: false, message: 'Máximo de solicitações é obrigatório quando habilitada' }
      },

      // Alerta de validação
      validationAlert: {
        show: false,
        message: 'Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados.'
      }
    }
  },

  async created() {
    // Verificar se offercourseid foi definido
    if (!this.offercourseid) {
      throw new Error('offercourseid não foi definido. Isso causará problemas ao salvar a turma.');
    }

    this.classData.offercourseid = parseInt(this.offercourseid);

    // Verificar se estamos no modo de edição
    // Primeiro verificamos se temos o classid como prop (nova abordagem)
    if (this.classid) {
      this.isEditing = true
      this.classId = parseInt(this.classid)
    }
    // Caso contrário, verificamos os query params (compatibilidade com código existente)
    else if (this.route.query.classid && this.route.query.edit === 'true') {
      this.isEditing = true
      this.classId = parseInt(this.route.query.classid)

      // Obter o ID da oferta
      const offerIdFromUrl = this.route.query.offerid || this.offerid

      // Redirecionar para a nova URL com parâmetros na rota
      this.$nextTick(() => {
        this.router.replace({
          name: 'EditClass',
          params: {
            offercourseid: this.offercourseid,
            classid: this.classId,
            offerid: offerIdFromUrl || '0'
          }
        })
      })
    }

    // Primeiro carregamos os dados iniciais (opções, etc.)
    await this.loadInitialData()

    // Se estiver no modo de edição, carregar os dados da turma
    if (this.isEditing && this.classId) {
      await this.loadClassData()

      // Garantir que os dados foram carregados antes de sincronizar situações
      this.syncSituations()

      // Reiniciar o componente para garantir que todos os dados sejam exibidos corretamente
      this.$nextTick(() => {
        this.restartComponent()
      })
    } else {
      // Se não estiver no modo de edição, apenas sincronizar situações
      this.syncSituations()
    }
  },

  mounted() {
    // Garantir que a página role para o topo quando for carregada
    window.scrollTo(0, 0)

    // Adicionar listener para cliques fora do dropdown de professores
    document.addEventListener('click', this.handleClickOutside)
  },

  beforeUnmount() {
    // Remover listener para cliques fora do dropdown
    document.removeEventListener('click', this.handleClickOutside)

    // Limpar timer de debounce se existir
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }
  },

  computed: {
    // Calcula o valor máximo permitido para o prazo de conclusão da turma
    maxEnrolPeriod() {
      // Só calcula se ambas as datas estiverem preenchidas e a data fim estiver habilitada
      if (this.classData.startdate && this.classData.optional_fields.enddate && this.classData.optional_fields.enableenddate) {
        // Calcula a diferença em dias entre as datas
        const daysDifference = this.calculateDaysDifference(
          this.classData.startdate,
          this.classData.optional_fields.enddate
        );

        // Retorna a diferença em dias como valor máximo (mínimo 1 dia)
        return daysDifference >= 1 ? daysDifference : 1;
      }

      // Se alguma das condições não for atendida, retorna null (sem limite)
      return null;
    },

    // Verifica se as datas de início e fim são iguais (turma de um dia)
    isOneDayClass() {
      // Só considera turma de um dia se:
      // 1. Data início estiver preenchida
      // 2. Data fim estiver habilitada E preenchida
      // 3. As duas datas forem iguais
      return this.classData.startdate &&
        this.classData.optional_fields.enableenddate &&
        this.classData.optional_fields.enddate &&
        this.classData.startdate === this.classData.optional_fields.enddate;
    },

    // Verifica se o prazo de conclusão deve estar desabilitado
    shouldDisableEnrolPeriod() {
      // Só desabilita se for realmente uma turma de um dia
      // (ambas as datas preenchidas e iguais)
      return this.isOneDayClass;
    }
  },

  watch: {
    // Observa mudanças nas situações selecionadas para prorrogação
    extensionSituations: {
      handler(newValue) {
        this.classData.optional_fields.extensionallowedsituations = newValue.map(item => item.value)
      },
      deep: true
    },

    // Observa mudanças nas situações selecionadas para rematrícula
    reenrolSituations: {
      handler(newValue) {
        this.classData.optional_fields.reenrolmentsituations = newValue.map(item => item.value)
      },
      deep: true
    },

    // Observa mudanças no estado de habilitação do prazo de conclusão
    'classData.optional_fields.enableenrolperiod': function (newValue) {
      // Se o prazo de conclusão for desabilitado, desabilita também a prorrogação
      if (!newValue && this.classData.optional_fields.enableextension) {
        this.classData.optional_fields.enableextension = false;
        // Exibe uma mensagem informativa para o usuário
        this.showWarningMessage('Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado.');
      }
    },

    // Observa mudanças na data de início da turma
    'classData.startdate': function () {
      // Valida a data fim quando a data início mudar (para verificar se fim >= início)
      if (this.classData.optional_fields.enableenddate && this.classData.optional_fields.enddate) {
        this.validateField('enddate');
      }

      // Se o prazo de conclusão estiver habilitado e tiver um valor, valida-o
      if (this.classData.optional_fields.enableenrolperiod && this.classData.optional_fields.enrolperiod) {
        this.validateField('enrolperiod');
      }

      // Valida pré-inscrição se estiver habilitada
      if (this.classData.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }

      // Verifica se deve desabilitar o prazo de conclusão (turma de um dia)
      this.checkAndDisableEnrolPeriodForOneDayClass();
    },

    // Observa mudanças na data fim da turma
    'classData.optional_fields.enddate': function () {
      // Valida a data fim quando ela mudar
      if (this.classData.optional_fields.enableenddate) {
        this.validateField('enddate');
      }

      // Se o prazo de conclusão estiver habilitado e tiver um valor, valida-o
      if (this.classData.optional_fields.enableenrolperiod && this.classData.optional_fields.enrolperiod) {
        this.validateField('enrolperiod');
      }

      // Valida pré-inscrição se estiver habilitada
      if (this.classData.optional_fields.enablepreenrolment && this.classData.optional_fields.enableenddate) {
        this.validatePreenrolmentDates();
      }

      // Verifica se deve desabilitar o prazo de conclusão (turma de um dia)
      this.checkAndDisableEnrolPeriodForOneDayClass();
    },

    // Observa mudanças na habilitação da data fim da turma
    'classData.optional_fields.enableenddate': function (newValue) {
      // Se a data fim for habilitada, valida-a imediatamente
      if (newValue && this.classData.optional_fields.enddate) {
        this.validateField('enddate');
      }

      // Se a data fim for desabilitada e o prazo de conclusão estiver habilitado, valida-o
      if (!newValue && this.classData.optional_fields.enableenrolperiod && this.classData.optional_fields.enrolperiod) {
        this.validateField('enrolperiod');
      }

      // Valida pré-inscrição se estiver habilitada
      if (this.classData.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }

      // Verifica se deve desabilitar o prazo de conclusão (turma de um dia)
      this.checkAndDisableEnrolPeriodForOneDayClass();
    },

    // Observa mudanças no valor do prazo de conclusão
    'classData.optional_fields.enrolperiod': function (newValue) {
      // Se o prazo de conclusão estiver habilitado, valida o campo
      if (this.classData.optional_fields.enableenrolperiod && newValue) {
        this.validateField('enrolperiod');
      }
    },

    'classData.optional_fields.preenrolmentstartdate': function () {
      if (this.classData.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }
    },
    'classData.optional_fields.preenrolmentenddate': function () {
      if (this.classData.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }
    },

  },

  methods: {
    /**
     * Calcula a diferença em dias entre duas datas
     * @param {string} startDate - Data de início no formato YYYY-MM-DD
     * @param {string} endDate - Data de fim no formato YYYY-MM-DD
     * @returns {number} - Diferença em dias entre as datas
     */
    calculateDaysDifference(startDate, endDate) {
      // Verifica se as datas são válidas
      if (!startDate || !endDate) {
        return 0;
      }

      // Converte as strings de data para objetos Date
      const start = new Date(startDate);
      const end = new Date(endDate);

      // Verifica se as datas são válidas
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return 0;
      }

      // Se as datas são iguais, retorna 1 (um dia de prazo)
      if (start.getTime() === end.getTime()) {
        return 1;
      }

      // Calcula a diferença em milissegundos
      const diffTime = Math.abs(end - start);

      // Converte para dias e arredonda para cima
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diffDays;
    },

    /**
     * Verifica se deve desabilitar o prazo de conclusão para turmas de um dia
     */
    checkAndDisableEnrolPeriodForOneDayClass() {
      // Se as datas são iguais (turma de um dia), desabilita o prazo de conclusão
      if (this.isOneDayClass && this.classData.optional_fields.enableenrolperiod) {
        this.classData.optional_fields.enableenrolperiod = false;
        this.classData.optional_fields.enrolperiod = null;

        // Também desabilita a prorrogação se estiver habilitada
        if (this.classData.optional_fields.enableextension) {
          this.classData.optional_fields.enableextension = false;
          this.classData.optional_fields.extensionperiod = null;
          this.classData.optional_fields.extensiondaysavailable = null;
          this.classData.optional_fields.extensionmaxrequests = null;
          this.classData.optional_fields.extensionallowedsituations = [];
        }

        // Exibe mensagem informativa
        this.showWarningMessage('Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim).');
      }
    },

    /**
     * Sincroniza as situações selecionadas com os arrays internos
     */
    syncSituations() {
      // Sincroniza as situações de prorrogação
      this.extensionSituations = this.classData.optional_fields.extensionallowedsituations.map(id => {
        // Para prorrogação, usamos a lista específica de situações
        const idNumber = parseInt(id);
        // Renomear "Matriculado" (id: 0) para "Inscrito"
        if (idNumber === 0) {
          return { value: idNumber, label: 'Inscrito' };
        }
        return this.extensionSituationOptions.find(option => option.value === idNumber) || { value: idNumber, label: `Situação ${id}` }
      })

      // Sincroniza as situações de rematrícula
      this.reenrolSituations = this.classData.optional_fields.reenrolmentsituations.map(id => {
        return this.situationOptions.find(option => option.value === parseInt(id)) || { value: parseInt(id), label: `Situação ${id}` }
      })
    },

    /**
     * Carrega todos os dados iniciais necessários
     */
    async loadInitialData() {
      try {
        this.loading = true

        // Carrega informações do curso da oferta
        await this.loadOfferCourse()

        // Carrega papéis disponíveis
        await this.loadRoles()

        // Carrega situações de matrícula
        await this.loadSituations()

        // Carrega métodos de inscrição disponíveis
        await this.loadEnrolmentMethods()

      } catch (error) {
        this.showErrorMessage('Alguns dados não puderam ser carregados. Valores padrão serão usados.')
      } finally {
        this.loading = false
      }
    },

    /**
     * Carrega informações do curso da oferta
     */
    async loadOfferCourse() {
      try {
        const response = await getCourse(this.offercourseid)
        if (!response.error && response.data) {
          this.offerCourse = response.data
        }
      } catch (error) {
        this.offerCourse = {
          id: this.offercourseid,
          offerid: null
        }
      }
    },

    /**
     * Carrega papéis disponíveis
     */
    async loadRoles() {
      const roles_response = await getCourseRoles(this.offercourseid)
      let roles = roles_response?.data;
      if (!roles_response.error && roles) {
        this.roleOptions = roles.map(role => ({
          value: role.id,
          label: role.name
        }))

        // Define o papel padrão apenas se não estiver definido
        if (!this.classData.optional_fields.roleid) {
          const studentRole = this.roleOptions.find(role => role.value === 5)
          this.classData.optional_fields.roleid = studentRole?.value ?? this.roleOptions[0].value
        }

        return
      }
    },

    /**
     * Carrega situações de matrícula
     */
    async loadSituations() {

      const response = await getSituationList()

      if (Array.isArray(response)) {
        const desiredSituationIds = [6, 7, 8, 4, 5];

        const filteredSituations = response.filter(situation =>
          desiredSituationIds.includes(situation.id)
        )

        this.situationOptions = filteredSituations.map(situation => ({
          value: situation.id,
          label: situation.name.charAt(0).toUpperCase() + situation.name.slice(1).toLowerCase()
        }))

        const extensionSituationIds = [0, 1];

        const filteredExtensionSituations = response.filter(situation =>
          extensionSituationIds.includes(situation.id)
        );

        this.extensionSituationOptions = filteredExtensionSituations.map(situation => ({
          value: situation.id,
          label: situation.name
        }));
      }
    },

    /**
     * Carrega métodos de inscrição disponíveis
     */
    async loadEnrolmentMethods() {
        const enrolTypeFromUrl = this.route.query.enrol_type;

        if (!this.isEditing && enrolTypeFromUrl) {
          this.classData.enrol = enrolTypeFromUrl;
        }
  
        const response = await getClassMethods(true)
    
        if (response && Array.isArray(response)) {
          this.enrolmentMethods = response.map(method => ({
            value: method.enrol,
            label: method.name
          }));
        }
      },

      /**
       * Valida dados obrigatórios
       */
      validate() {
        // Resetar todos os erros
        Object.keys(this.formErrors).forEach(key => {
          this.formErrors[key].hasError = false;
        });

        // Resetar alerta de validação
        this.validationAlert.show = false;

        // Flag para controlar se há erros
        let hasErrors = false;

        // O campo enrol não é mais validado, pois é definido no modal e não é editável

        if (!this.classData.classname) {
          this.formErrors.classname.hasError = true;
          hasErrors = true;
        }

        if (!this.classData.startdate) {
          this.formErrors.startdate.message = 'Data de início é obrigatória';
          this.formErrors.startdate.hasError = true;
          hasErrors = true;
        } else if (this.classData.optional_fields.enableenddate &&
          this.classData.optional_fields.enddate &&
          new Date(this.classData.startdate) > new Date(this.classData.optional_fields.enddate)) {
          this.formErrors.startdate.message = 'Data de início deve ser igual ou anterior à data fim da turma';
          this.formErrors.startdate.hasError = true;
          hasErrors = true;
        } else {
          this.formErrors.startdate.hasError = false;
        }

        if (!this.classData.optional_fields.roleid) {
          this.formErrors.roleid.hasError = true;
          hasErrors = true;
        }

        // Validação de data fim se estiver habilitada
        if (this.classData.optional_fields.enableenddate) {
          if (!this.classData.optional_fields.enddate) {
            this.formErrors.enddate.message = 'Data fim da turma é obrigatória quando habilitada';
            this.formErrors.enddate.hasError = true;
            hasErrors = true;
          } else if (this.classData.startdate && new Date(this.classData.optional_fields.enddate) < new Date(this.classData.startdate)) {
            this.formErrors.enddate.message = 'Data fim da turma deve ser igual ou posterior à data de início';
            this.formErrors.enddate.hasError = true;
            hasErrors = true;
          } else {
            this.formErrors.enddate.hasError = false;
          }
        }

        // Validação de datas de pré-inscrição se habilitadas
        if (this.classData.optional_fields.enablepreenrolment) {
          if (!this.classData.optional_fields.preenrolmentstartdate) {
            this.formErrors.preenrolmentstartdate.hasError = true;
            hasErrors = true;
          }
          if (!this.classData.optional_fields.preenrolmentenddate) {
            this.formErrors.preenrolmentenddate.hasError = true;
            hasErrors = true;
          }
        }

        const preenrolmentValid = this.validatePreenrolmentDates();
        if (!preenrolmentValid) {
          hasErrors = true;
        }

        // Validação de prazo de conclusão se habilitado
        if (this.classData.optional_fields.enableenrolperiod) {
          // Verifica se o campo está vazio
          if (!this.classData.optional_fields.enrolperiod) {
            this.formErrors.enrolperiod.message = 'Prazo de conclusão é obrigatório quando habilitado';
            this.formErrors.enrolperiod.hasError = true;
            hasErrors = true;
          }
          // Verifica se o valor excede o máximo permitido
          else if (this.maxEnrolPeriod !== null && parseInt(this.classData.optional_fields.enrolperiod) > this.maxEnrolPeriod) {
            this.formErrors.enrolperiod.message = `Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`;
            this.formErrors.enrolperiod.hasError = true;
            hasErrors = true;
          }
        }

        // Validação de campos de prorrogação se habilitada e se o prazo de conclusão estiver habilitado
        if (this.classData.optional_fields.enableextension && this.classData.optional_fields.enableenrolperiod) {
          if (!this.classData.optional_fields.extensionperiod) {
            this.formErrors.extensionperiod.hasError = true;
            hasErrors = true;
          }
          if (!this.classData.optional_fields.extensiondaysavailable) {
            this.formErrors.extensiondaysavailable.hasError = true;
            hasErrors = true;
          }
          if (!this.classData.optional_fields.extensionmaxrequests) {
            this.formErrors.extensionmaxrequests.hasError = true;
            hasErrors = true;
          }
        }

        // Se houver erros, mostrar alerta
        if (hasErrors) {
          this.validationAlert.show = true;
          this.showErrorMessage(this.validationAlert.message);

          // Rolar para o topo para mostrar o alerta
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        }

        return !hasErrors;
      },

      /**
       * Valida um campo específico
       * @param {string} field Nome do campo a ser validado
       */
      validateField(field) {
        // Validar campo específico
        switch (field) {
          case 'enrol':
            // O campo enrol não é mais validado, pois é definido no modal e não é editável
            this.formErrors.enrol.hasError = false;
            break;
          case 'classname':
            this.formErrors.classname.hasError = !this.classData.classname;
            break;
          case 'startdate':
            // Verifica se o campo está preenchido
            const startdateHasValue = this.classData.startdate;

            // Verifica se a data início é maior que a data fim (quando data fim estiver habilitada)
            const startdateAfterEnddate = startdateHasValue &&
              this.classData.optional_fields.enableenddate &&
              this.classData.optional_fields.enddate &&
              new Date(this.classData.startdate) > new Date(this.classData.optional_fields.enddate);

            if (!startdateHasValue) {
              this.formErrors.startdate.message = 'Data de início é obrigatória';
              this.formErrors.startdate.hasError = true;
            } else if (startdateAfterEnddate) {
              this.formErrors.startdate.message = 'Data de início deve ser igual ou anterior à data fim da turma';
              this.formErrors.startdate.hasError = true;
            } else {
              this.formErrors.startdate.message = 'Data de início é obrigatória';
              this.formErrors.startdate.hasError = false;
            }
            break;
          case 'roleid':
            this.formErrors.roleid.hasError = !this.classData.optional_fields.roleid;
            break;
          case 'enddate':
            // Verifica se o campo está habilitado e se tem valor
            const enddateEnabled = this.classData.optional_fields.enableenddate;
            const enddateHasValue = this.classData.optional_fields.enddate;

            // Verifica se a data fim é menor que a data início
            const enddateBeforeStartdate = enddateEnabled && enddateHasValue && this.classData.startdate &&
              new Date(this.classData.optional_fields.enddate) < new Date(this.classData.startdate);

            if (enddateEnabled && !enddateHasValue) {
              this.formErrors.enddate.message = 'Data fim da turma é obrigatória quando habilitada';
              this.formErrors.enddate.hasError = true;
            } else if (enddateBeforeStartdate) {
              this.formErrors.enddate.message = 'Data fim da turma deve ser igual ou posterior à data de início';
              this.formErrors.enddate.hasError = true;
            } else {
              this.formErrors.enddate.message = 'Data fim da turma é obrigatória quando habilitada';
              this.formErrors.enddate.hasError = false;
            }
            break;
          case 'preenrolmentstartdate':
            this.formErrors.preenrolmentstartdate.hasError = this.classData.optional_fields.enablepreenrolment && !this.classData.optional_fields.preenrolmentstartdate;
            this.validatePreenrolmentDates();
            break;
          case 'preenrolmentenddate':
            this.formErrors.preenrolmentenddate.hasError = this.classData.optional_fields.enablepreenrolment && !this.classData.optional_fields.preenrolmentenddate;
            this.validatePreenrolmentDates();
            break;
          case 'enrolperiod':
            // Verifica se o campo está habilitado e se tem valor
            const isEnabled = this.classData.optional_fields.enableenrolperiod;
            const hasValue = this.classData.optional_fields.enrolperiod !== null &&
              this.classData.optional_fields.enrolperiod !== undefined &&
              this.classData.optional_fields.enrolperiod !== '';

            // Verifica se o valor excede o máximo permitido
            const exceedsMaximum = this.maxEnrolPeriod !== null &&
              hasValue &&
              parseInt(this.classData.optional_fields.enrolperiod) > this.maxEnrolPeriod;

            // Define a mensagem de erro apropriada
            if (isEnabled && !hasValue) {
              this.formErrors.enrolperiod.message = 'Prazo de conclusão é obrigatório quando habilitado';
              this.formErrors.enrolperiod.hasError = true;
            } else if (isEnabled && exceedsMaximum) {
              this.formErrors.enrolperiod.message = `Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`;
              this.formErrors.enrolperiod.hasError = true;
            } else {
              this.formErrors.enrolperiod.message = 'Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma';
              this.formErrors.enrolperiod.hasError = false;
            }
            break;
          case 'extensionperiod':
            this.formErrors.extensionperiod.hasError = this.classData.optional_fields.enableextension && this.classData.optional_fields.enableenrolperiod && !this.classData.optional_fields.extensionperiod;
            break;
          case 'extensiondaysavailable':
            this.formErrors.extensiondaysavailable.hasError = this.classData.optional_fields.enableextension && this.classData.optional_fields.enableenrolperiod && !this.classData.optional_fields.extensiondaysavailable;
            break;
          case 'extensionmaxrequests':
            this.formErrors.extensionmaxrequests.hasError = this.classData.optional_fields.enableextension && this.classData.optional_fields.enableenrolperiod && !this.classData.optional_fields.extensionmaxrequests;
            break;
        }

        // Verificar se ainda há campos com erro
        const hasErrors = Object.values(this.formErrors).some(error => error.hasError);

        // Atualizar alerta de validação
        this.validationAlert.show = hasErrors;

        return !this.formErrors[field].hasError;
      },

      validatePreenrolmentDates() {

        let isValid = true;

        // Resetar erros
        this.formErrors.preenrolmentstartdate.hasError = false;
        this.formErrors.preenrolmentenddate.hasError = false;

        // Só validar se pré-inscrição estiver habilitada
        if (this.classData.optional_fields.enablepreenrolment) {
          const startDate = this.classData.startdate;
          const endDate = this.classData.optional_fields.enableenddate
            ? this.classData.optional_fields.enddate
            : null;
          const preStart = this.classData.optional_fields.preenrolmentstartdate;
          const preEnd = this.classData.optional_fields.preenrolmentenddate;

          const courseStartDate = this.offerCourse.startdate;
          const courseEndDate = this.offerCourse.enddate;

          // Validar se as datas foram preenchidas
          if (!preStart) {
            this.formErrors.preenrolmentstartdate.hasError = true;
            this.formErrors.preenrolmentstartdate.message = 'Data início de pré-inscrição é obrigatória';
            isValid = false;
          }

          if (!preEnd) {
            this.formErrors.preenrolmentenddate.hasError = true;
            this.formErrors.preenrolmentenddate.message = 'Data fim de pré-inscrição é obrigatória';
            isValid = false;
          }

          // Validar que preenrolmentenddate > preenrolmentstartdate
          if (new Date(preEnd) < new Date(preStart)) {
            this.formErrors.preenrolmentenddate.hasError = true;
            this.formErrors.preenrolmentenddate.message = 'Data fim deve ser posterior à data início';
            isValid = false;
          }

          // Validar que preenrolmentstartdate > startdate
          if (new Date(preStart) > new Date(startDate)) {
            this.formErrors.preenrolmentstartdate.hasError = true;
            this.formErrors.preenrolmentstartdate.message = 'Data início deve ser igual ou anterior à data início da turma';
            isValid = false;
          }

          // Validar que preenrolmentstartdate < courseStartdate
          if (new Date(preStart) < new Date(courseStartDate)) {
            this.formErrors.preenrolmentstartdate.hasError = true;
            this.formErrors.preenrolmentstartdate.message = 'Data início deve ser igual ou posterior à data início do curso';
            isValid = false;
          }

          // Validar que preenrolmentstartdate > courseEnddate
          if (courseEndDate && new Date(preStart) > new Date(courseEndDate)) {
            this.formErrors.preenrolmentstartdate.hasError = true;
            this.formErrors.preenrolmentstartdate.message = 'Data de início deve ser igual ou anterior à data fim do curso';
            isValid = false;
          }
          // Se enddate estiver habilitada, validar que preenrolmentenddate <= enddate
          if (endDate && new Date(preEnd) >= new Date(endDate)) {
            this.formErrors.preenrolmentenddate.hasError = true;
            this.formErrors.preenrolmentenddate.message = 'Data fim deve ser anterior à data fim da turma';
            isValid = false;
          }

          // Validar que preenrolmentenddate > courseEnddate
          if (courseEndDate && new Date(preEnd) > new Date(courseEndDate)) {
            this.formErrors.preenrolmentenddate.hasError = true;
            this.formErrors.preenrolmentenddate.message = 'Data fim deve ser igual ou anterior à data fim do curso';
            isValid = false;
          }
        }
        return isValid;
      },

    /**
     * Carrega os dados de uma turma existente para edição
     */
    async loadClassData() {
        try {
          this.loading = true;

          const response = await getClass(this.classId);
          if (!response) {
            throw new Error('Erro ao carregar dados da turma: resposta vazia');
          }

          // Processa os dados básicos da turma
          await this.processClassData(response);

          // Atualiza a UI após o processamento
          this.updateUIAfterLoading();

          // Atualiza o título da página
          document.title = `Editar Turma: ${this.classData.classname}`;

        } catch (error) {
          this.showErrorMessage('Erro ao carregar dados da turma. Alguns campos podem estar incompletos.');
        } finally {
          this.loading = false;
        }
      },

    /**
     * Processa os dados principais da turma
     */
    async processClassData(response) {
        // Extrai os dados da resposta
        const classData = this.debugApiResponse(response);

        // Processa campos básicos
        this.processBasicFields(classData);

        // Processa campos opcionais
        if (classData.optional_fields) {
          this.processOptionalFields(classData.optional_fields);
        }

        // Processa professores
        if (classData.teachers && Array.isArray(classData.teachers)) {
          this.selectedTeachers = classData.teachers
        }
      },

      /**
       * Processa os campos básicos da turma
       */
      processBasicFields(classData) {
        this.classData.classname = classData.classname || classData.name || '';
        this.classData.enrol = classData.enrol || classData.enrol_type || '';
        this.classData.offercourseid = classData.offercourseid || this.offercourseid;
        this.classData.startdate = classData.startdate;
      },

      /**
       * Processa os campos opcionais da turma
       */
      processOptionalFields(optionalFields) {

        // Copia todos os campos opcionais válidos
        Object.keys(optionalFields).forEach(key => {
          if (key in this.classData.optional_fields) {
            this.classData.optional_fields[key] = optionalFields[key];
          }
        });

        // Processa grupos específicos de campos opcionais
        this.processDateFields(optionalFields);
        this.processEnrolmentFields(optionalFields);
        this.processUserLimits(optionalFields);
        this.processDescriptionAndRole(optionalFields);
        this.processReenrolment(optionalFields);
        this.processExtensionFields(optionalFields);
      },

      /**
       * Processa campos relacionados a datas
       */
      processDateFields(fields) {
        // Data de fim
        if (fields.enableenddate) {
          this.classData.optional_fields.enableenddate = true;
          this.classData.optional_fields.enddate = fields.enddate || null;
        }

        // Pré-inscrição
        if (fields.enablepreenrolment) {
          this.classData.optional_fields.enablepreenrolment = true;
          this.classData.optional_fields.preenrolmentstartdate = fields.preenrolmentstartdate || null;
          this.classData.optional_fields.preenrolmentenddate = fields.preenrolmentenddate || null;
        }
      },

      /**
       * Processa campos de matrícula
       */
      processEnrolmentFields(fields) {
        // Prazo de conclusão
        if (fields.enableenrolperiod) {
          this.classData.optional_fields.enableenrolperiod = true;
          this.classData.optional_fields.enrolperiod = fields.enrolperiod > 0 ? fields.enrolperiod : null;
        } else {
          this.classData.optional_fields.enrolperiod = null;
        }
      },

      /**
       * Processa limites de usuários
       */
      processUserLimits(fields) {
        // Vagas mínimas e máximas
        this.classData.optional_fields.minusers = fields.minusers > 0 ? fields.minusers : null;
        this.classData.optional_fields.maxusers = fields.maxusers > 0 ? fields.maxusers : null;
      },

      /**
       * Processa descrição e papel padrão
       */
      processDescriptionAndRole(fields) {
        this.classData.optional_fields.roleid = fields.roleid || null;
        this.classData.optional_fields.description = fields.description || '';
      },

      /**
       * Processa rematrícula
       */
      processReenrolment(fields) {
        if (fields.enablereenrol) {
          this.classData.optional_fields.enablereenrol = true;
          this.classData.optional_fields.reenrolmentsituations = fields.reenrolmentsituations || [];

          // Atualiza o array de situações para o Autocomplete
          if (Array.isArray(fields.reenrolmentsituations)) {
            this.reenrolSituations = fields.reenrolmentsituations.map(id => {
              const situation = this.situationOptions.find(s => s.value === parseInt(id));
              return situation || { value: parseInt(id), label: `Situação ${id}` };
            });
          }
        } else {
          this.classData.optional_fields.reenrolmentsituations = [];
        }
      },

      /**
       * Processa campos de prorrogação
       */
      processExtensionFields(fields) {
        if (fields.enableextension && fields.enableenrolperiod) {
          this.classData.optional_fields.enableextension = true;
          this.processExtensionPeriods(fields);
          this.processExtensionSituations(fields);
        } else {
          this.resetExtensionFields();
        }
      },

      /**
       * Processa períodos de prorrogação
       */
      processExtensionPeriods(fields) {
        this.classData.optional_fields.extensionperiod = fields.extensionperiod > 0 ? fields.extensionperiod : null;
        this.classData.optional_fields.extensiondaysavailable = fields.extensiondaysavailable > 0 ? fields.extensiondaysavailable : null;
        this.classData.optional_fields.extensionmaxrequests = fields.extensionmaxrequests > 0 ? fields.extensionmaxrequests : null;
      },

      /**
       * Processa situações de prorrogação
       */
      processExtensionSituations(fields) {
        if (Array.isArray(fields.extensionallowedsituations)) {
          this.extensionSituations = fields.extensionallowedsituations.map(id => {
            const idNumber = parseInt(id);
            if (idNumber === 0) return { value: idNumber, label: 'Inscrito' };
            if (idNumber === 1) {
              const situation = this.extensionSituationOptions.find(s => s.value === idNumber);
              return situation || { value: idNumber, label: 'Em andamento' };
            }
            return { value: idNumber, label: `Situação ${id}` };
          });
        }
      },

      /**
       * Reseta campos de prorrogação
       */
      resetExtensionFields() {
        this.classData.optional_fields.extensionperiod = null;
        this.classData.optional_fields.extensiondaysavailable = null;
        this.classData.optional_fields.extensionmaxrequests = null;
        this.classData.optional_fields.extensionallowedsituations = [];
      },

      handleTeacherInput() {
        const term = this.teacherSearchTerm.trim();

        if (this.debounceTimer) {
          clearTimeout(this.debounceTimer);
        }

        if (term.length >= 3) {
          this.showTeacherDropdown = true;
          this.highlightedIndex = -1;
          this.debounceTimer = setTimeout(async () => {
            await this.fetchPotentialTeachers(term);
          }, 500);
        } else {
          this.showTeacherDropdown = false;
          this.teacherList = [];
        }
      },

    /**
     * Recupera lista de usuários potenciais para adicionar como professor
     */
    async fetchPotentialTeachers(searchString) {
        let selectedTeacherIds = this.selectedTeachers.map(teacher => teacher.id || teacher.value) ?? [];

        const teachersResponse = await getPotentialTeachers(
          this.offercourseid,
          this.classId,
          searchString,
          selectedTeacherIds
        );

        this.teacherList = !teachersResponse.error && teachersResponse.data
          ? teachersResponse.data
          : []
        ;
      },

      /**
       * Remove usuário da lista de professor
       */
      removeTeacher(teacherId) {
        this.selectedTeachers = this.selectedTeachers.filter(teacher => teacher.value !== teacherId);
      },

      /**
       * Manipula o foco no campo de busca de professores
       */
      handleTeacherInputFocus() {
        if (this.teacherSearchTerm.length >= 3 && this.teacherList.length > 0) {
          this.showTeacherDropdown = true;
        }
      },

      /**
       * Seleciona um professor do dropdown
       */
      selectTeacher(teacher) {
        // Adiciona o professor à lista de selecionados
        this.selectedTeachers.push({
          id: teacher.id,
          value: teacher.id,
          fullname: teacher.fullname,
          email: teacher.email
        });

        // Limpa o campo de busca e esconde o dropdown
        this.teacherSearchTerm = '';
        this.showTeacherDropdown = false;
        this.highlightedIndex = -1;
        this.teacherList = [];

        // Foca novamente no campo de busca para facilitar a adição de mais professores
        this.$nextTick(() => {
          if (this.$refs.teacherSearchInput) {
            this.$refs.teacherSearchInput.focus();
          }
        });
      },

      /**
       * Manipula navegação por teclado no dropdown
       */
      handleKeydown(event) {
        if (!this.showTeacherDropdown || this.teacherList.length === 0) {
          return;
        }

        switch (event.key) {
          case 'ArrowDown':
            event.preventDefault();
            this.highlightedIndex = Math.min(this.highlightedIndex + 1, this.teacherList.length - 1);
            break;
          case 'ArrowUp':
            event.preventDefault();
            this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
            break;
          case 'Enter':
            event.preventDefault();
            if (this.highlightedIndex >= 0 && this.highlightedIndex < this.teacherList.length) {
              this.selectTeacher(this.teacherList[this.highlightedIndex]);
            }
            break;
          case 'Escape':
            event.preventDefault();
            this.showTeacherDropdown = false;
            this.highlightedIndex = -1;
            break;
        }
      },

      /**
       * Manipula cliques fora do dropdown para escondê-lo
       */
      handleClickOutside(event) {
        if (this.$refs.teacherSearchContainer && !this.$refs.teacherSearchContainer.contains(event.target)) {
          this.showTeacherDropdown = false;
          this.highlightedIndex = -1;
        }
      },

      /**
       * Atualiza a UI após o carregamento dos dados
       */
      updateUIAfterLoading() {
        this.$nextTick(() => {
          this.updateFormFields();
          this.$forceUpdate();

          if (!this.classData.classname || !this.classData.enrol) {
            this.showErrorMessage('Dados incompletos após carregamento.');
          }
        });
      },

      /**
       * Atualiza os campos do formulário manualmente
       */
      updateFormFields() {
        this.updateSelectField('enrolSelect', this.classData.enrol);
        this.updateInputField('classnameInput', this.classData.classname);
        this.updateInputField('startdateInput', this.classData.startdate);
      },

      /**
       * Atualiza um campo de seleção
       */
      updateSelectField(ref, value) {
        if (this.$refs[ref]) {
          this.$refs[ref].value = value;
          const event = new Event('change');
          this.$refs[ref].$el.dispatchEvent(event);
        }
      },

      /**
       * Atualiza um campo de input
       */
      updateInputField(ref, value) {
        if (this.$refs[ref] && value) {
          this.$refs[ref].value = value;
          const event = new Event('input');
          this.$refs[ref].$el.dispatchEvent(event);
        }
      },

    /**
     * Salva a turma
     */

    async saveClass() {
        if (!this.validate()) return

        try {
          this.loading = true

          // Cria uma cópia dos dados para não modificar o estado original
          const dataCopy = JSON.parse(JSON.stringify(this.classData))

          // Atualiza professores baseado no Autocomplete
          dataCopy.teachers = this.selectedTeachers.map(t => t.value)

          // Garante que o campo enrol (tipo de inscrição) esteja definido
          if (!dataCopy.enrol) {
            // Tenta obter o tipo de inscrição da URL
            const enrolTypeFromUrl = this.route.query.enrol_type;
            if (enrolTypeFromUrl) {
              dataCopy.enrol = enrolTypeFromUrl;
            } else {
              // Se não encontrar na URL, usa o valor padrão
              dataCopy.enrol = 'offer_manual';
            }
          }

          // Limpa campos numéricos quando não estão habilitados ou quando o prazo de conclusão está desabilitado
          if (!dataCopy.optional_fields.enableextension || !dataCopy.optional_fields.enableenrolperiod) {
            // Se a prorrogação estiver desabilitada ou o prazo de conclusão estiver desabilitado, limpa todos os campos de prorrogação
            dataCopy.optional_fields.extensionperiod = undefined
            dataCopy.optional_fields.extensiondaysavailable = undefined
            dataCopy.optional_fields.extensionmaxrequests = undefined
            dataCopy.optional_fields.extensionallowedsituations = []
            // Garante que a prorrogação esteja desabilitada se o prazo de conclusão estiver desabilitado
            if (!dataCopy.optional_fields.enableenrolperiod) {
              dataCopy.optional_fields.enableextension = false
            }
          } else {
            dataCopy.optional_fields.extensionallowedsituations = this.extensionSituations.map(item => item.value)
          }

          if (!dataCopy.optional_fields.enableenrolperiod) {
            dataCopy.optional_fields.enrolperiod = undefined
          }

          if (!dataCopy.optional_fields.enablereenrol) {
            dataCopy.optional_fields.reenrolmentsituations = []
          } else {
            dataCopy.optional_fields.reenrolmentsituations = this.reenrolSituations.map(item => item.value)
          }

          // Limpa campos numéricos vazios ou com valor 0
          const numericFields = ['enrolperiod', 'extensionperiod', 'extensiondaysavailable', 'extensionmaxrequests', 'minusers', 'maxusers']
          numericFields.forEach(field => {
            const value = dataCopy.optional_fields[field]
            if (value === 0 || value === null || value === '' || value === undefined) {
              dataCopy.optional_fields[field] = undefined
            }
          });

          // Remover explicitamente o campo enrol_type se existir
          if ('enrol_type' in dataCopy) {
            delete dataCopy.enrol_type;

          }

          // Remover o campo enrol se estiver no modo de edição
          // O backend não espera esse parâmetro na atualização, apenas na criação
          if (this.isEditing && 'enrol' in dataCopy) {
            // Remover o campo para evitar erro de parâmetro inválido
            delete dataCopy.enrol;
          }

          // Verificar se todos os campos obrigatórios estão presentes
          // No modo de edição, não precisamos do campo 'enrol' pois ele foi removido
          const requiredFields = this.isEditing
            ? ['offercourseid', 'classname', 'startdate']
            : ['offercourseid', 'classname', 'startdate', 'enrol'];
          const missingFields = requiredFields.filter(field => !dataCopy[field]);

          if (missingFields.length > 0) {

            this.showErrorMessage(`Campos obrigatórios ausentes: ${missingFields.join(', ')}`);
            this.loading = false;
            return;
          }

          // Garantir que offercourseid seja um número
          if (dataCopy.offercourseid) {
            dataCopy.offercourseid = parseInt(dataCopy.offercourseid);
          }



          const data = dataCopy;
          let response;

          if (this.isEditing && this.classId) {
            // Adiciona o ID da turma para atualização
            data.offerclassid = this.classId

            // Chama a API para atualizar a turma existente
            let response = await updateClass(data)
            if (!response.error && response.data) {
              this.showSuccessMessage(response.data.message)
              // Permanece na mesma página após atualizar
              // Recarrega os dados da turma para exibir as alterações
              this.loadClassData()
            }
            else {
              this.showErrorMessage(response.exception.message)
            }
          } else {
            // Envia para API para criar uma nova turma
            response = await addClass(data)

            if (!response.error && response.data) {
              this.showSuccessMessage(response.data.message)
              let turmaId = response.data.offerclassid;

              // Redireciona para a página de edição da turma recém-criada
              // Atualiza o modo para edição
              this.isEditing = true
              this.classId = turmaId
              // Usar o router para navegar para a página de edição da turma
              this.router.push({
                name: 'EditClass',
                params: {
                  offercourseid: this.offercourseid,
                  classid: turmaId,
                  offerid: this.offerid || this.route.query.offerid || '0'
                }
              })
            }
            else {
              this.showErrorMessage(response.exception.message)
            }
          }
        } catch (error) {
          this.showErrorMessage(error.message || 'Erro ao salvar turma')
        } finally {
          this.loading = false
        }
      },

      /**
       * Volta para tela da oferta
       */
      goBack() {
        // Primeiro, verifica se temos o ID da oferta como prop (nova abordagem)
        if (this.offerid) {
          this.router.push({
            name: 'editar-oferta',
            params: { id: this.offerid }
          })
        }
        // Verifica se temos o ID da oferta no curso da oferta
        else if (this.offerCourse && this.offerCourse.offerid) {
          this.router.push({
            name: 'editar-oferta',
            params: { id: this.offerCourse.offerid }
          })
        } else {
          // Se não tiver, tenta obter da URL
          const offerIdFromUrl = this.route.query.offerid
          if (offerIdFromUrl) {
            this.router.push({
              name: 'editar-oferta',
              params: { id: offerIdFromUrl }
            })
          } else {
            // Se não tiver, volta para a lista de ofertas
            this.router.push({ name: 'listar-ofertas' })
          }
        }
      },

      /**
       * Exibe mensagem de erro
       * @param {string} message Mensagem a ser exibida
       */
      showErrorMessage(message) {
        // Limpa qualquer timeout anterior para esconder o toast
        if (this.toastTimeout) {
          clearTimeout(this.toastTimeout)
          this.toastTimeout = null
        }

        // Garante que o toast esteja escondido antes de mostrar novamente
        this.showToast = false

        // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
        this.$nextTick(() => {
          this.toastMessage = message
          this.toastType = 'error'
          this.showToast = true

          // Define um novo timeout para esconder o toast
          this.toastTimeout = setTimeout(() => {
            this.showToast = false
          }, 3000)
        })
      },

      /**
       * Exibe mensagem de sucesso
       * @param {string} message Mensagem a ser exibida
       */
      showSuccessMessage(message) {
        // Limpa qualquer timeout anterior para esconder o toast
        if (this.toastTimeout) {
          clearTimeout(this.toastTimeout)
          this.toastTimeout = null
        }

        // Garante que o toast esteja escondido antes de mostrar novamente
        this.showToast = false

        // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
        this.$nextTick(() => {
          this.toastMessage = message
          this.toastType = 'success'
          this.showToast = true

          // Define um novo timeout para esconder o toast
          this.toastTimeout = setTimeout(() => {
            this.showToast = false
          }, 3000)
        })
      },

      /**
       * Exibe mensagem de aviso
       * @param {string} message Mensagem a ser exibida
       */
      showWarningMessage(message) {
        // Limpa qualquer timeout anterior para esconder o toast
        if (this.toastTimeout) {
          clearTimeout(this.toastTimeout)
          this.toastTimeout = null
        }

        // Garante que o toast esteja escondido antes de mostrar novamente
        this.showToast = false

        // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
        this.$nextTick(() => {
          this.toastMessage = message
          this.toastType = 'warning'
          this.showToast = true

          // Define um novo timeout para esconder o toast
          this.toastTimeout = setTimeout(() => {
            this.showToast = false
          }, 3000)
        })
      },

      /**
       * Método para depurar os valores dos campos
       * Pode ser chamado no console do navegador: app.$refs.classView.debugFields()
       */
      debugFields() {

        return {
          isEditing: this.isEditing,
          classId: this.classId,
          classname: this.classData.classname,
          enrol: this.classData.enrol,
          startdate: this.classData.startdate,
          teachers: this.classData.teachers,
          optional_fields: this.classData.optional_fields
        }
      },

      /**
       * Método para atualizar manualmente os campos do formulário
       * Este método é chamado após o carregamento dos dados da turma
       */
      updateFormFields() {
        // Atualizar campo de método de inscrição
        if (this.$refs.enrolSelect && this.classData.enrol) {

          // Tentar diferentes abordagens para atualizar o campo
          try {
            // Abordagem 1: Atualizar diretamente o valor do componente
            this.$refs.enrolSelect.value = this.classData.enrol

            // Abordagem 2: Disparar evento de mudança
            const changeEvent = new Event('change')
            this.$refs.enrolSelect.$el.dispatchEvent(changeEvent)

            // Abordagem 3: Atualizar o modelo e forçar atualização
            this.$refs.enrolSelect.$emit('input', this.classData.enrol)
            this.$refs.enrolSelect.$forceUpdate()
          } catch (error) {
            // Erro ao atualizar campo de método de inscrição
          }
        }

        // Atualizar campo de nome da turma
        if (this.$refs.classnameInput && this.classData.classname) {

          try {
            // Atualizar diretamente o valor do componente
            this.$refs.classnameInput.value = this.classData.classname

            // Disparar evento de input
            const inputEvent = new Event('input')
            this.$refs.classnameInput.$el.dispatchEvent(inputEvent)

            // Atualizar o modelo e forçar atualização
            this.$refs.classnameInput.$emit('input', this.classData.classname)
            this.$refs.classnameInput.$forceUpdate()
          } catch (error) {
            // Erro ao atualizar campo de nome da turma
          }
        }

        // Atualizar campo de data de início
        if (this.$refs.startdateInput && this.classData.startdate) {

          try {
            // Atualizar diretamente o valor do componente
            this.$refs.startdateInput.value = this.classData.startdate

            // Disparar evento de input
            const inputEvent = new Event('input')
            this.$refs.startdateInput.$el.dispatchEvent(inputEvent)

            // Atualizar o modelo e forçar atualização
            this.$refs.startdateInput.$emit('input', this.classData.startdate)
            this.$refs.startdateInput.$forceUpdate()
          } catch (error) {
            // Erro ao atualizar campo de data de início
          }
        }

        // Forçar atualização do componente principal
        this.$forceUpdate()
      },

      /**
       * Seleciona todas as situações para prorrogação
       */
      handleSelectAllExtensionSituations() {
        // Verifica se todas as situações já estão selecionadas
        const allSelected = this.extensionSituationOptions.every(option =>
          this.extensionSituations.some(selected => selected.value === option.value)
        );

        if (allSelected) {
          // Se todas já estão selecionadas, desmarca todas
          this.extensionSituations = [];
        } else {
          // Caso contrário, seleciona todas as situações disponíveis
          this.extensionSituations = [...this.extensionSituationOptions];
        }

        // Atualiza o array de situações permitidas no objeto de dados
        this.classData.optional_fields.extensionallowedsituations = this.extensionSituations.map(item => item.value);
      },

      /**
       * Seleciona todas as situações para rematrícula
       */
      handleSelectAllReenrolSituations() {
        // Verifica se todas as situações já estão selecionadas
        const allSelected = this.situationOptions.every(option =>
          this.reenrolSituations.some(selected => selected.value === option.value)
        );

        if (allSelected) {
          // Se todas já estão selecionadas, desmarca todas
          this.reenrolSituations = [];
        } else {
          // Caso contrário, seleciona todas as situações disponíveis
          this.reenrolSituations = [...this.situationOptions];
        }

        // Atualiza o array de situações permitidas no objeto de dados
        this.classData.optional_fields.reenrolmentsituations = this.reenrolSituations.map(item => item.value);
      },

      /**
       * Método para reiniciar o componente
       * Este método é chamado após o carregamento dos dados da turma
       * para garantir que todos os campos sejam atualizados corretamente
       */
      restartComponent() {
        // Garantir que a página role para o topo
        window.scrollTo(0, 0)

        // Atualizar manualmente os campos do formulário
        this.updateFormFields()

        // Forçar atualização do componente
        this.$forceUpdate()

        // Adicionar um pequeno atraso e forçar nova atualização
        setTimeout(() => {
          this.updateFormFields()
          this.$forceUpdate()

          // Garantir novamente que a página role para o topo após a atualização
          window.scrollTo(0, 0)
        }, 500)
      },

      /**
       * Método para depurar a resposta da API
       * Este método analisa a estrutura da resposta e extrai os dados relevantes
       * @param {Object} response - A resposta da API
       * @returns {Object} - Os dados extraídos da resposta
       */
      debugApiResponse(response) {

        // Verificar se a resposta existe
        if (!response) {
          return null
        }

        // Verificar se a resposta tem a estrutura { error: false, data: { ... } }
        if (response.error === false && response.data) {
          // Verificar campos principais
          const data = response.data
          return data
        }

        // Se a resposta for direta
        return response
      },

      /**
       * Método para tentar uma abordagem alternativa de carregamento de dados
       * Este método é chamado quando o carregamento normal falha
       * @param {Object} response - A resposta original da API
       */
      tryAlternativeDataLoading(response) {
        try {
          // Tentar extrair dados diretamente da resposta
          if (response.data && typeof response.data === 'object') {
            // Verificar se há um campo 'id' na resposta
            if (response.data.id) {
              // Preencher dados básicos
              if (response.data.classname) {
                this.classData.classname = response.data.classname
              }

              if (response.data.enrol) {
                this.classData.enrol = response.data.enrol
              }

              if (response.data.startdate) {
                this.classData.startdate = response.data.startdate
              }

              // Preencher campos opcionais
              if (response.data.optional_fields) {
                // Copiar todos os campos opcionais
                Object.keys(response.data.optional_fields).forEach(key => {
                  if (key in this.classData.optional_fields) {
                    this.classData.optional_fields[key] = response.data.optional_fields[key]
                  }
                })
              }

              // Preencher professores
              if (response.data.teachers && Array.isArray(response.data.teachers)) {
                this.classData.teachers = [...response.data.teachers]
              }
            }
          }
        } catch (error) {
          // Erro ao tentar abordagem alternativa
        }
      },

      // Métodos relacionados ao modal de matrículas removidos
    }
  }
</script>

<style lang="scss" scoped>
.new-class {
  margin-bottom: 2rem;
}

.page-header-container {
  padding: 0 1rem;
  margin-bottom: 1rem;
}

.validation-alert {
  background-color: #332701;
  border: 1px solid #997404;
  color: #ffda6a;
  padding: 1rem;
  margin: 0 1rem 1rem 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.75rem;

  i {
    font-size: 1.25rem;
  }

  span {
    flex: 1;
  }
}

.section-container {
  margin: 0;
  background-color: #212529;
  border-radius: 4px;
  padding: 0 1rem;
}

.section-title {
  color: var(--primary);
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  margin: 0;
}

.form-label {
  display: inline-block;
  color: #fff;
  font-size: 14px;
  margin-bottom: 0;
  margin-right: 5px;
}

.label-with-help {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;

  .form-label {
    margin-right: 2px;
  }
}

.input-with-help {
  display: flex;
  align-items: center;
}

.checkbox-container {
  display: flex;
  align-items: center;
}

.input-with-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
}

.inline-checkbox {
  margin-left: 10px;
}

.text-editor-container {
  max-width: 702px;
}

.actions-container {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.fa-exclamation-circle {
  margin-right: 0;
}

.required-fields-message {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.form-info {
  background-color: #212529;
  color: #fff;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.limited-width-select {
  max-width: 500px;
}

.limited-width-input {
  max-width: 180px;
}

.limited-width-editor {
  max-width: 700px;
}

.date-input {
  :deep(input[type="date"]) {
    color-scheme: dark;

    /* Esconde o ícone nativo do calendário */
    &::-webkit-calendar-picker-indicator {
      opacity: 0;
    }
  }

  :deep(.calendar-icon) {
    color: #fff;
    z-index: 1;
  }
}

/* Estilos para campos desabilitados */
.form-group {
  &.disabled {

    .form-label,
    .checkbox-label {
      opacity: 0.65;
    }

    img.help-icon {
      opacity: 0.65;
    }

    .custom-input,
    .custom-select,
    .editor-container,
    .form-control {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }

  /* Estilo para campos dependentes quando o campo principal está desabilitado */
  &.dependent-field {
    &.disabled {
      position: relative;
    }
  }
}

/* Estilos para o dropdown de professores */
.custom-input-container {
  position: relative;
}

.teacher-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  border-top: none;
  border-radius: 0 0 4px 4px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;

  &.no-results {
    padding: 12px 16px;
    text-align: center;
    font-style: italic;
  }
}

.teacher-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.15s ease;

  &:last-child {
    border-bottom: none;
  }
}

</style>