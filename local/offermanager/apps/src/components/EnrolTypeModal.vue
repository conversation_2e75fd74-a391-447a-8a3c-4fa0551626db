<template>
  <div
    v-if="show"
    class="modal-backdrop"
    @click="closeOnBackdrop ? $emit('close') : null"
  >
    <div class="modal-container" :class="[`modal-${size}`]" @click.stop>
      <!-- Cabeçalho do modal -->
      <div class="modal-header">
        <h3 class="modal-title">{{ title }}</h3>
        <button class="modal-close" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Corpo do modal -->
      <div class="modal-body">
        <div class="enrol-type-modal">
          <p class="modal-description">
            Selecione o tipo de inscrição para esta turma. Esta configuração não
            poderá ser alterada posteriormente.
          </p>

          <div class="form-group mb-3">
            <div class="label-with-help">
              <label class="form-label">Método de inscrição</label>
              <i
                class="icon fa fa-exclamation-circle text-danger fa-fw m-0"
                title="Obrigatório"
                role="img"
                aria-label="Obrigatório"
              ></i>
              <HelpIcon
                title="Ajuda com método de inscrição"
                text="Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.
Inscrição manual em ofertas: Nesta opção o usuário será matriculado manualmente (em lote ou individualmente) por um perfil autorizado, através da página 'Usuários matriculados' contida em cada turma.
Autoisncrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no curso para fazer sua matrícula em uma turma. Ou seja, ele não será inscrito automaticamente como no Tipo de inscrição 'Inscrição por público-alvo em ofertas'."
              />
            </div>
            <div class="limited-width-input" style="max-width: 280px">
              <CustomSelect
                v-model="selectedEnrolType"
                :options="[
                  { value: '', label: 'Selecione um método...' },
                  ...enrolmentMethods,
                ]"
                :width="280"
                required
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Rodapé do modal -->
      <div class="modal-footer">
        <div class="form-info">
          <span style="color: #f8f9fa; font-size: 15px"
            >Este formulário contém campos obrigatórios marcados com</span
          >
          <i
            class="fas fa-exclamation-circle"
            style="color: #dc3545; font-size: 0.85rem; vertical-align: middle"
          ></i>
        </div>
        <div class="footer-buttons">
          <button
            class="btn btn-primary"
            @click="handleConfirm"
            :disabled="!selectedEnrolType"
          >
            {{ confirmButtonText }}
          </button>
          <button class="btn btn-secondary" @click="$emit('close')">
            {{ cancelButtonText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomSelect from "./CustomSelect.vue";
import HelpIcon from "./HelpIcon.vue";
import { getClassMethods } from "@/services/offer";

export default {
  name: "EnrolTypeModal",

  components: {
    CustomSelect,
    HelpIcon,
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "Tipo de Inscrição",
    },
    size: {
      type: String,
      default: "md",
      validator: (value) => ["sm", "md", "lg", "xl"].includes(value),
    },
    closeOnBackdrop: {
      type: Boolean,
      default: true,
    },
    confirmButtonText: {
      type: String,
      default: "Continuar",
    },
    cancelButtonText: {
      type: String,
      default: "Cancelar",
    },
    offercourseid: {
      type: [Number, String],
      required: true,
    },
    offerid: {
      type: [Number, String],
      required: false,
      default: "0",
    },
  },

  emits: ["close", "confirm"],

  data() {
    return {
      selectedEnrolType: "",
      enrolmentMethods: [],
      loading: false,
    };
  },

  mounted() {
    // Inicializar popovers quando o componente é montado
    this.$nextTick(() => {
      this.initializePopovers();
    });
  },

  watch: {
    show(newVal) {
      if (newVal) {
        this.loadEnrolmentMethods();
        // Inicializar popovers após o DOM ser atualizado
        this.$nextTick(() => {
          this.initializePopovers();
        });
      }
    },
  },

  methods: {
    async loadEnrolmentMethods() {
      try {
        this.loading = true;
        const response = await getClassMethods(true); // Apenas métodos habilitados

        if (response && Array.isArray(response.data)) {
          this.enrolmentMethods = response.data.map((method) => ({
            value: method.enrol,
            label: method.name,
          }));
        }
      } catch (error) {
        console.error("Erro ao carregar métodos de inscrição:", error);
      } finally {
        this.loading = false;
      }
    },

    handleConfirm() {
      if (!this.selectedEnrolType) return;

      this.$emit("confirm", {
        enrolType: this.selectedEnrolType,
        offercourseid: this.offercourseid,
        offerid: this.offerid,
      });
    },

    initializePopovers() {
      // Verifica se jQuery e Bootstrap estão disponíveis
      if (typeof $ !== "undefined" && typeof $.fn.popover !== "undefined") {
        // Inicializa os popovers do Bootstrap
        $('[data-toggle="popover"]').popover({
          container: "body",
          trigger: "focus",
          html: true,
        });
      } else {
        console.warn(
          "jQuery ou Bootstrap não estão disponíveis para inicializar popovers"
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  background-color: #1e1e1e;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  color: #fff;
}

.modal-sm {
  width: 300px;
}

.modal-md {
  width: 500px;
}

.modal-lg {
  width: 800px;
}

.modal-xl {
  width: 1100px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #333;
  background-color: #252525;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
  color: #fff;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #aaa;

  &:hover {
    color: #fff;
  }
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  background-color: #1e1e1e;
}

.modal-footer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-top: 1px solid #333;
  background-color: #252525;

  .footer-buttons {
    display: flex;
    gap: 10px;
    margin-left: auto;
  }
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  border: none;
}

.btn-primary {
  &:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.65;
  }
}

.form-info {
  font-size: 0.9rem;
  color: #aaa;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-description {
  margin-bottom: 20px;
  color: #ddd;
}

.label-with-help {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0;
  color: #fff;
}

.limited-width-input {
  width: 100%;
}
</style>
